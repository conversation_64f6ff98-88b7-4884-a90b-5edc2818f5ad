
from fastapi import APIRouter, Depends, Query, HTTPException, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import Employee, Business, Location, Opportunity
from app.validations import StringValidate
from app.exceptions import RecordNotFoundException, CustomValidationError
from app.helper import WildcardAuth<PERSON>elper
from app.constants import <PERSON>de<PERSON>ame, PermissionName
from peewee import fn
from typing import Optional
import logging

router = APIRouter(
    prefix="/locations",
    tags=["Wildcard Locations API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


# @before_actions
async def get_opportunity(
    jobId: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Opportunity:
    """
    Retrieve an Opportunity by its ID.

    Args:
        jobId (int): The ID of the Opportunity to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Opportunity: The Opportunity object if found.

    Raises:
        RecordNotFoundException: If the Opportunity does not exist.
    """
    opportunity = Opportunity.get_or_none(id=jobId, business_id=business.id)
    if not opportunity:
        raise RecordNotFoundException(message="Opportunity does not exist")
    return opportunity

# Helper function to get location by ID
async def get_location(
    id: int, business: Business = Depends(WildcardAuthHelper.validate_subdomain)
) -> Location:
    """
    Retrieve a location by its ID.

    Args:
        id (int): The ID of the location to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Location: The location object if found.

    Raises:
        RecordNotFoundException: If the location does not exist.
    """
    location = Location.get_or_none(id=id, business_id=business.id, is_deleted=0)
    if not location:
        raise RecordNotFoundException(message="Location does not exist")
    return location


# ------------------------------ router functions ------------------------------


@router.get(
    "",
    summary="List of locations",
    description="Retrieve a paginated list of locations.",
    response_model=PaginationResponse,
)
def get_locations(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(None, description="Search term to filter locations"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of locations.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of locations per page. Defaults to 10.
        search (Optional[str]): Search term to filter locations.

    Returns:
        PaginationResponse: Paginated list of locations.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_LOCATIONS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        base_query = Location.select().where(
            Location.business_id == business.id, Location.is_deleted == 0
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.lower(Location.address).contains(search))

        total_records = base_query.count()

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit).order_by(Location.id.desc())
        # Prepare location list
        rows = [record.info() for record in records]
        # Prepare response
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in location list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post(
    "",
    summary="Create New Location",
    description="Create a new Location with the provided information.",
    response_model=SuccessResponse,
)
async def create_location(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Endpoint to register a new Location.

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing location information.

    Returns:
        SuccessResponse: Success message along with location details upon successful registration.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_LOCATIONS,
        perm_names=PermissionName.READ_WRITE,
    )

    try:
        # Validate location data
        address = StringValidate(
            body.get("address"),
            field="Address",
            required=True,
            max_length=100,
            strip=True,
        )
        city = StringValidate(
            body.get("city"), field="City", required=True, max_length=100, strip=True
        )
        state = StringValidate(
            body.get("state"), field="state", required=True, max_length=100, strip=True
        )
        country = StringValidate(
            body.get("country"),
            field="Country",
            required=True,
            max_length=100,
            strip=True,
        )
        pincode = StringValidate(
            body.get("pincode"),
            field="Pincode",
            required=True,
            max_length=10,
            strip=True,
        )

        exists = (
            Location.select()
            .where(
                fn.Lower(Location.address) == address.lower(),
                fn.Lower(Location.city) == city.lower(),
                fn.Lower(Location.state) == state.lower(),
                fn.Lower(Location.country) == country.lower(),
                fn.Lower(Location.pincode) == pincode.lower(),
                Location.business_id == current_employee.business_id,
            )
            .exists()
        )

        if exists:
            raise CustomValidationError(
                error="Location already exists with this address."
            )
        else:
            location = Location.create(
                address=address,
                city=city,
                state=state,
                country=country,
                pincode=pincode,
                business_id=current_employee.business_id,
            )

        # Return success response with location details
        return SuccessResponse(
            message="Location Created Successfully.", data=location.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}",
    summary="Get Location",
    description="Retrieve location details.",
    response_model=SuccessResponse,
)
async def get_location_detail(
    request: Request, location: Location = Depends(get_location)
):
    """
    Endpoint to retrieve location details.

    Args:
        location (location): The location instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with location details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_LOCATIONS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        # Return success response
        return SuccessResponse(
            message="location details fetched successfully.", data=location.info()
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/{id}",
    summary="Update Location",
    description="Update an existing location with the provided information.",
    response_model=SuccessResponse,
)
async def update_location(
    request: Request,
    location: Location = Depends(get_location),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Update an existing location with the provided information.

    Args:
        location (Location): The location instance to be updated, fetched by the `get_location` dependency.
        current_employee (Employee): The current authenticated employee, fetched by the `WildcardAuthHelper.get_current_employee` dependency.
        body (dict): A dictionary containing the new location data.

    Returns:
        SuccessResponse: The response containing the updated location details.

    Raises:
        HTTPException: If there is an error during the update process, an HTTP 422 Unprocessable Entity exception is raised.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_LOCATIONS,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        # Validate and sanitize input data
        address = StringValidate(
            body.get("address"),
            field="Address",
            required=True,
            max_length=100,
            strip=True,
        )
        city = StringValidate(
            body.get("city"), field="City", required=True, max_length=100, strip=True
        )
        state = StringValidate(
            body.get("state"), field="State", required=True, max_length=100, strip=True
        )
        country = StringValidate(
            body.get("country"),
            field="Country",
            required=True,
            max_length=100,
            strip=True,
        )
        pincode = StringValidate(
            body.get("pincode"),
            field="Pincode",
            required=True,
            max_length=10,
            strip=True,
        )

        exists = (
            Location.select()
            .where(
                fn.Lower(Location.address) == address.lower(),
                fn.Lower(Location.city) == city.lower(),
                fn.Lower(Location.state) == state.lower(),
                fn.Lower(Location.country) == country.lower(),
                fn.Lower(Location.pincode) == pincode.lower(),
                Location.business_id == current_employee.business_id,
            )
            .where(Location.id != location.id)
            .exists()
        )

        if exists:
            raise CustomValidationError(
                error="Location already exists with this address."
            )
        else:
            # Update location fields
            location.address = address
            location.city = city
            location.state = state
            location.country = country
            location.pincode = pincode
            location.save()

        # Return success response with updated location details
        return SuccessResponse(
            message="Location Updated Successfully.", data=location.info()
        )
    except Exception as e:
        # Log the error and raise HTTP exception
        logging.error(f"Error updating location: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.delete(
    "/{id}",
    summary="Delete Location",
    description="Delete the location by marking them as deleted in the database.",
    response_model=SuccessResponse,
)
async def update_location_status(
    request: Request, location: Location = Depends(get_location)
):
    """
    Delete the location by marking them as deleted in the database.

    Args:
        location: The location instance, provided by dependency injection.

    Returns:
        SuccessResponse: A success message indicating the location was deleted.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_LOCATIONS,
        perm_names=PermissionName.READ_DELETE,
    )

    try:
        location.is_deleted = True
        location.save()

        return SuccessResponse(
            message="Location deleted successfully.", data=location.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/options/all",
    summary="Get Locations as Options",
    description="Retrieve an options list of locations.",
    response_model=SuccessResponse,
)
def get_location_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(None, description="Search term to filter location"),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve an options list of locations.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of locations per page. Defaults to 10.
        search (Optional[str]): Search term to filter locations.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
        SuccessResponse: The response containing the list of location options.
    """
    try:
        base_query = Location.select().where(
            (Location.business_id == business.id) & (Location.is_deleted == 0)
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                (fn.LOWER(Location.address).contains(search))
                | (fn.LOWER(Location.city).contains(search))
                | (fn.LOWER(Location.state).contains(search))
                | (fn.LOWER(Location.country).contains(search))
            )

        if showId is not None:
            # Explicitly handling priority ordering
            base_query = base_query.order_by(
                fn.IF(Location.id == showId, 0, 1), Location.id.desc()
            )
        else:
            base_query = base_query.order_by(Location.address.asc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [
            {
                "label": record.full_address,
                "value": record.id,
                "disabled": record.is_deleted == 1,
            }
            for record in records
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in location options list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
    

# get locations based on Job ID 
@router.get(
    "/options/jobs/{jobId}",
    summary="Get Locations as Options",
    description="Retrieve an options list of locations.",
    response_model=SuccessResponse,
)
def get_location_options_by_job(
    request: Request,
    jobId: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    opportunity: Opportunity = Depends(get_opportunity), 
    search: str = Query(None, description="Search keyword for location"),
):
    """
    Retrieve an options list of locations with optional search.

    Args:
        jobId: The job ID to retrieve locations for.
        search: Optional keyword to filter locations.
        
    Returns:
        SuccessResponse: The response containing the list of location options.
    """
    try:
        logging.info(f"Opportunity Location IDs: {opportunity.location_ids}")

        location_ids = opportunity.location_ids
        if not location_ids:
            return SuccessResponse(data=[], message="No locations found for the job")

        base_query = Location.select().where(Location.id.in_(location_ids))

        # Apply search filters if search term provided
        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                (fn.LOWER(Location.address).contains(search)) |
                (fn.LOWER(Location.city).contains(search)) |
                (fn.LOWER(Location.state).contains(search)) |
                (fn.LOWER(Location.country).contains(search))
            )

        rows = [
            {
                "label": location.full_address,
                "value": location.id,
            }
            for location in base_query
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")

    except Exception as e:
        logging.error(f"Exception in location options list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
