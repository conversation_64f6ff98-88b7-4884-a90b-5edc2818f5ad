import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { LOCATION } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to create a new location
const createLocation = (params: any) => {
  return API.post(LOCATION, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get the list of location
const getLocationList = (params: any) => {
  return API.get(LOCATION, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get location options of a business
const getLocationOptions = (data: any) => {
  return API.get(`${LOCATION}/options/all`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get Location detail
const getLocationDetail = (id: number) => {
  return API.get(`${LOCATION}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a location
const updateLocation = (id: number, data: any) => {
  return API.put(`${LOCATION}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to delete a location
const deleteLocation = (id: number) => {
  return API.delete(`${LOCATION}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get Opportunities location options of a business
const getOpportunitiesLocationOptions = (jobId: any, data :any) => {
  return API.get(`${LOCATION}/options/jobs/${jobId}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  createLocation,
  getLocationList,
  getLocationDetail,
  updateLocation,
  getLocationOptions,
  deleteLocation,
  getOpportunitiesLocationOptions,
};
